<?php

namespace App\Rules;

use App\Services\PricingRuleConflictService;
use Illuminate\Contracts\Validation\Rule;

class PricingRuleConflictRule implements Rule
{
    protected string $type;

    protected float $otherValue;

    protected ?string $message = null;

    private PricingRuleConflictService $conflictService;

    /**
     * Create a new rule instance.
     *
     * @param  string  $type  The type of pricing rule ('base' or 'distance')
     * @param  float  $otherValue  The value of the other pricing rule
     */
    public function __construct(string $type, float $otherValue)
    {
        $this->type = $type;
        $this->otherValue = $otherValue;
        $this->conflictService = new PricingRuleConflictService;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     */
    public function passes($attribute, $value): bool
    {
        $value = (float) $value;

        $conflicts = $this->type === 'base'
            ? $this->conflictService->checkForConflicts($value, $this->otherValue)
            : $this->conflictService->checkForConflicts($this->otherValue, $value);

        if (empty($conflicts)) {
            return true;
        }

        $this->message = $this->buildErrorMessage($conflicts);

        return false;
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return $this->message ?? 'The pricing rule conflicts with existing component pricing.';
    }

    /**
     * Build a detailed error message from the conflicts.
     */
    private function buildErrorMessage(array $conflicts): string
    {
        $typeLabel = $this->type === 'base' ? 'base fare' : 'distance fare';

        // Start with a more user-friendly introduction
        $message = "<div class='space-y-4'>";
        $message .= "<p class='font-medium text-warning-600'>Lowering the global {$typeLabel} will result in pricing issues in the following components:</p>";

        $conflictTypes = [
            'areas' => 'Areas',
            'vehicle_types' => 'Vehicle Types',
            'seat_numbers' => 'Seat Configurations',
            'gender_rules' => 'Gender Rules',
            'equipment' => 'Equipment',
            'day_time_configs' => 'Day-Time Configurations',
        ];

        foreach ($conflictTypes as $key => $title) {
            if (! isset($conflicts[$key]) || empty($conflicts[$key])) {
                continue;
            }

            // Add a section title with proper styling
            $message .= "<div class='mt-3'>";
            $message .= "<h3 class='text-sm font-semibold text-gray-900 dark:text-white mb-2'>Affected {$title}:</h3>";
            $message .= "<ul class='space-y-2 pl-0'>";

            foreach ($conflicts[$key] as $item) {
                $message .= "<li class='flex items-start'>";

                // Add appropriate label based on conflict type with better formatting
                if ($key === 'areas' || $key === 'vehicle_types') {
                    $itemName = $item['name'];
                } elseif ($key === 'seat_numbers') {
                    $itemName = "{$item['seats_number']} seats";
                } elseif ($key === 'gender_rules') {
                    $itemName = $item['gender'];
                } else {
                    $itemName = 'Unknown';
                }

                // Add conflict details if they exist for the current type
                if (isset($item['conflicts']["{$this->type}_fare"])) {
                    $conflictInfo = $item['conflicts']["{$this->type}_fare"];
                    $currentValue = number_format((float) $conflictInfo['current_value'], 2);
                    $minAllowed = number_format((float) $conflictInfo['min_allowed'], 2);

                    $message .= "<span class='block'><span class='font-medium'>{$itemName}</span> — Current: <span class='text-danger-600'>{$currentValue} LYD</span>, Minimum allowed: <span class='text-success-600'>{$minAllowed} LYD</span></span>";
                } else {
                    $message .= "<span>{$itemName}</span>";
                }

                $message .= '</li>';
            }

            $message .= '</ul>';
            $message .= '</div>';
        }

        // Add a clear call to action with an emoji for emphasis
        $message .= "<div class='mt-4 pt-3 border-t border-gray-200 dark:border-gray-700'>";
        $message .= "<p class='flex items-start'>";
        $message .= "<span>Please update the {$typeLabel}s for the components mentioned above before reducing the global {$typeLabel}.</span>";
        $message .= '</p>';
        $message .= '</div>';

        $message .= '</div>';

        return $message;
    }
}
